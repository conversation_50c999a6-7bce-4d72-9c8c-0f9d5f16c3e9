
import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import useSignIn from 'react-auth-kit/hooks/useSignIn';
import useSignOut from 'react-auth-kit/hooks/useSignOut';
import useIsAuthenticated from 'react-auth-kit/hooks/useIsAuthenticated';
import useAuthUser from 'react-auth-kit/hooks/useAuthUser';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';

import {LoginFormValues, SignupFormValues} from '../utils/auth-schemas';
import {authService} from '@/services/authService';
import {toast} from "sonner";
import {useRoles} from '../context/RoleContext';
import {stripeService} from '@/services/stripeService';

interface Plan {
  id: number;
  name: string;
  max_services: number;
  max_addresses: number;
  max_servicemen: number;
  max_service_packages: number;
  price: string;
  duration: string;
  description: string;
  status: number;
  created_by: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

interface CurrentSubscriptionType {
  plan: Plan;
  start_date: string;
  end_date: string;
  is_active: number;
  allowed_max_services: number;
  allowed_max_addresses: number;
  allowed_max_servicemen: number;
  allowed_max_service_packages: number;
}

// User type
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  plan?: string;
  current_subscription?: CurrentSubscriptionType | null;
  role: {
    id: number;
    name: string;
    guard_name: string;
  }
}

export function useAuth() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // React Auth Kit hooks
  const signIn = useSignIn();
  const signOut = useSignOut();
  const isAuthenticatedValue = useIsAuthenticated();
  // Get user from auth state
  const user = useAuthUser<User>();
  const token = useAuthHeader();

  // Get roles from RoleContext
  const {
    roles,
    hasRole,
    isProvider: isProviderRole,
    isCustomer: isCustomerRole,
    getDashboardRoute,
    refreshRoles,
    loading: rolesLoading
  } = useRoles();

  // Enhanced authentication methods - useIsAuthenticated returns a boolean directly
  const isAuthenticated = isAuthenticatedValue;

  // Check if user has provider role - directly return boolean
  const isProvider = isAuthenticated && isProviderRole();

  // Check if user has customer role - directly return boolean
  const isCustomer = isAuthenticated && isCustomerRole();

  // Check if user has admin role - directly return boolean
  const isAdmin = isAuthenticated && hasRole('admin');


  // Login with email and password
  const loginWithEmail = useCallback(async (data: LoginFormValues, redirectUrl?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call the login API
      const response = await authService.login({
        email: data.email,
        password: data.password,
      });

      // Extract token and user data from response
      const { access_token,user } = response.data;
      const isSignInSuccessful = signIn({
        auth:{
          token: access_token,
        },
        userState: {
          id: user.id,
          email: data.email,
          name: user.name,
          role: {
            id: user.role.id,
            name: user.role.name,
            guard_name: user.role.guard_name,
          },
          current_subscription: user.current_subscription,
        },
      })

      // Sign in with React Auth Kit
      if (isSignInSuccessful) {
        // Fetch user roles after successful login
        await refreshRoles();

        setIsLoading(false);

        // Redirect to the specified URL if provided, otherwise to the dashboard
        if (redirectUrl) {
          navigate(redirectUrl);
        } else {
          // Redirect to the appropriate dashboard based on user role
          navigate(getDashboardRoute(user.role));
        }

        return { success: true };
      } else {
        throw new Error('Failed to sign in');
      }
    } catch (err) {
      toast.error('Login failed. Please check your credentials and try again.');
      setError('Login failed. Please check your credentials and try again.');
      setIsLoading(false);
      return { success: false, error: 'Login failed' };
    }
  }, [navigate, signIn]);

  // Signup with email
  const signupWithEmail = useCallback(async (data: SignupFormValues, redirectUrl?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call the signup API
      const response = await authService.signup({
        name: data.name.split('@')[0], // Extract name from email
        email: data.email,
        password: data.password,
        password_confirmation: data.password,
        roleId: data.roleId,
      });

      // Extract token and user data from response
      const { access_token,user } = response.data;

      // Sign in with React Auth Kit
      if (signIn({
        auth:{
          token: access_token,
          type: 'Bearer',
        },
        userState: {
          id: user.id,
          email: data.email,
          name: user.name,
          role: {
            id: user.role.id,
            name: user.role.name,
            permissions: user.role.permissions,
          }
        },
      })) {
        // Fetch user roles after successful signup
        await refreshRoles();

        setIsLoading(false);

        // Check for Stripe redirection parameters
        const urlParams = new URLSearchParams(window.location.search);
        const redirectToStripe = urlParams.get('redirectToStripe');
        const priceId = urlParams.get('priceId');
        const productId = urlParams.get('productId');

        if (redirectToStripe === 'true' && priceId && productId) {
          // User signed up for a Stripe plan, redirect to checkout
          try {
            const sessionResponse = await stripeService.createCheckoutSession(
              {
                userId: user.id,
                priceId,
                productId,
              },
              access_token
            );

            if (sessionResponse.success && sessionResponse.data?.sessionId) {
              // Redirect to Stripe Checkout
              window.location.href = sessionResponse.data.url;
              return { success: true };
            } else {
              toast.error('Failed to create checkout session. Please try again.');
              navigate('/for-providers');
            }
          } catch (error) {
            console.error('Error creating checkout session after signup:', error);
            toast.error('Error processing subscription. Please try again.');
            navigate('/for-providers');
          }
        } else if (redirectUrl) {
          navigate(redirectUrl);
        } else {
          // Redirect to the appropriate dashboard based on user role
          navigate(getDashboardRoute(user.role));
        }

        return { success: true };
      } else {
        throw new Error('Failed to sign in after signup');
      }
    } catch (err) {
      console.error('Signup failed:', err);
      setError('Signup failed. Please try again later.');
      setIsLoading(false);
      return { success: false, error: 'Signup failed' };
    }
  }, [navigate, signIn]);

  // Social authentication (Google, Apple, etc.)
  const authenticateWithProvider = useCallback(async (provider: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Normalize provider name to lowercase for consistent handling
      const normalizedProvider = provider.toLowerCase();

      // In a real app, you would implement OAuth flow here
      console.log(`Authenticating with ${provider}`);

      // Mock successful authentication for now
      // This would be replaced with actual OAuth implementation
      const mockUser = {
        id: '1',
        name: `${provider} User`,
        email: `user@${normalizedProvider}.com`,
        accountType: 'customer' as const,
      };

      // Sign in with React Auth Kit
      if (signIn({
        auth:{
          token: 'mock-token', // Replace with actual token from provider
          type: 'Bearer',
        },
        userState: user,
      })) {
        setIsLoading(false);

        // Redirect to dashboard or home page
        navigate('/dashboard');

        return { success: true };
      } else {
        throw new Error(`Failed to sign in with ${provider}`);
      }
    } catch (err) {
      console.error(`${provider} authentication failed:`, err);
      setError(`${provider} authentication failed. Please try again.`);
      setIsLoading(false);
      return { success: false, error: `${provider} authentication failed` };
    }
  }, [navigate, signIn, user]);

  // Google authentication
  const authenticateWithGoogle = useCallback(async (idToken: string, roleId?: number, redirectUrl?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call the googleLogin API
      const response = await authService.googleLogin({
        id_token: idToken,
        roleId
      });

      // Extract token and user data from response
      const { access_token, user } = response.data;

      const isSignInSuccessful = signIn({
        auth:{
          token: access_token,
          type: 'Bearer',
        },
        userState: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: {
            id: user.role.id,
            name: user.role.name,
            guard_name: user.role.guard_name,
          }
        },
      });

      // Sign in with React Auth Kit
      if (isSignInSuccessful) {
        // Fetch user roles after successful login
        await refreshRoles();

        setIsLoading(false);

        // Redirect to the specified URL if provided, otherwise to the dashboard
        if (redirectUrl) {
          navigate(redirectUrl);
        } else {
          // Redirect to the appropriate dashboard based on user role
          navigate(getDashboardRoute(user.role));
        }

        return { success: true };
      } else {
        throw new Error('Failed to sign in with Google');
      }
    } catch (err) {
      console.error('Google authentication failed:', err);
      setError('Google authentication failed. Please try again.');
      setIsLoading(false);
      return { success: false, error: 'Google authentication failed' };
    }
  }, [navigate, signIn, refreshRoles, getDashboardRoute]);

  // Logout
  const logout = useCallback(() => {
    // Sign out with React Auth Kit
    signOut();

    // Clear any localStorage items that might have been set
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userType');
    localStorage.removeItem('userName');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('authToken');

    // Show success toast
    toast.success('Logged out successfully');

    // Redirect to home page
    navigate('/');
  }, [navigate, signOut]);

  // Update user information
  const updateUser = useCallback((userData: Partial<User>) => {
    if (!user) return false;

    // Use the existing signIn hook to update the user information
    // but keep the current token
    return signIn({
      auth: {
        token: token?.replace('Bearer ', '') || '', // Keep the current token
        type: 'Bearer',
      },
      userState: {
        ...user,
        ...userData,
      },
    });
  }, [signIn, user, token]);

  // Update avatar specifically
  const updateAvatar = useCallback((avatarUrl: string) => {
    if (!user) return false;

    const success = updateUser({ avatar: avatarUrl });

    if (success) {
      toast.success('Avatar updated successfully');
    } else {
      toast.error('Failed to update avatar');
    }

    return success;
  }, [updateUser, user]);

  return {
    isAuthenticated,
    user,
    isLoading,
    error,
    loginWithEmail,
    signupWithEmail,
    authenticateWithProvider,
    authenticateWithGoogle,
    logout,
    updateUser,
    updateAvatar,
    // Role-related functions
    roles,
    hasRole,
    isProvider,
    isCustomer,
    isAdmin,
    getDashboardRoute,
    refreshRoles,
    token,
    rolesLoading
  };
}
