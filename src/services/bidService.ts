import { apiService, ApiResponse } from './api';
import {
  Bid,
  CreateBidRequest,
  UpdateBidRequest,
  UpdateBidStatusRequest,
  BidFilters,
  BidSortOptions,
  BidListResponse,
  BidStatistics,
  BidActionResponse
} from '../types/bid';

/**
 * Create a new bid - Enhanced for Task 17 Subtask 17.2 & 17.3
 * Implements comprehensive error handling and business logic validation
 */
export const createBid = async (bidData: CreateBidRequest): Promise<ApiResponse<Bid>> => {
  try {
    // Client-side validation before API call
    if (!bidData.jobId || !bidData.amount || !bidData.description || !bidData.estimated_completion_time) {
      return {
        data: null,
        error: 'Missing required fields: jobId, amount, description, and estimated_completion_time are required',
        status: 400,
        isSuccess: false
      };
    }

    if (bidData.amount <= 0) {
      return {
        data: null,
        error: 'Bid amount must be greater than 0',
        status: 400,
        isSuccess: false
      };
    }

    if (bidData.description.trim().length < 10) {
      return {
        data: null,
        error: 'Bid description must be at least 10 characters long',
        status: 400,
        isSuccess: false
      };
    }

    // Validate estimated_completion_time is a valid ISO date string
    const completionDate = new Date(bidData.estimated_completion_time);
    if (isNaN(completionDate.getTime())) {
      return {
        data: null,
        error: 'Invalid estimated completion time format. Please provide a valid date.',
        status: 400,
        isSuccess: false
      };
    }

    // Make API call with enhanced error handling using new endpoint
    const response = await apiService<Bid>(`/api/job-bookings/${bidData.jobId}/bids`, {
      method: 'POST',
      body: {
        amount: bidData.amount,
        description: bidData.description.trim(),
        estimated_completion_time: bidData.estimated_completion_time
      },
      requiresAuth: true
    });

    // Enhanced response handling
    if (response.isSuccess && response.data) {
      return {
        ...response,
        data: {
          ...response.data,
          jobId: response.data.jobId || bidData.jobId, // Ensure jobId is present
          status: response.data.status || 'pending' // Default status mapping
        }
      };
    }

    // Handle specific error cases
    if (response.status === 401) {
      return {
        ...response,
        error: 'Authentication required. Please log in to submit a bid.'
      };
    }

    if (response.status === 403) {
      return {
        ...response,
        error: 'You do not have permission to bid on this job.'
      };
    }

    if (response.status === 409) {
      return {
        ...response,
        error: 'You have already submitted a bid for this job.'
      };
    }

    return response;

  } catch (error) {
    console.error('Bid creation error:', error);
    return {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred while creating the bid',
      status: 0,
      isSuccess: false
    };
  }
};

/**
 * Get bids for a provider
 */
export const getProviderBids = async (
  providerId: string,
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidListResponse>(`/api/providers/${providerId}/bids?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Get bids for the authenticated provider
 * This endpoint uses the authenticated provider's ID automatically
 * As per BID_API_DOCUMENTATION.md section 4.5
 */
export const getCurrentProviderBids = async (
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  perPage = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidListResponse>(`/api/provider/bids?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Get bids for a specific job
 */
export const getJobBids = async (
  jobId: string,
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidListResponse>(`/api/jobs/${jobId}/bids?${params}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Get bids for a specific service request
 * This endpoint retrieves a paginated list of bids for a specific service request
 * Accessible by the Consumer who owns the service request or an Admin
 * As per BID_API_DOCUMENTATION.md section 4.6
 */
export const getServiceRequestBids = async (
  serviceRequestId: string,
  page = 1,
  perPage = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  // Validate service request ID
  if (!serviceRequestId || serviceRequestId.trim() === '') {
    return {
      data: null,
      error: 'Service request ID is required',
      status: 400,
      isSuccess: false
    };
  }

  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString()
  });

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  try {
    return await apiService<BidListResponse>(`/api/service-requests/${serviceRequestId}/bids?${params}`, {
      method: 'GET',
      requiresAuth: true,
      headers
    });
  } catch (error) {
    console.error('Error fetching service request bids:', error);
    return {
      data: null,
      error: 'Failed to fetch service request bids',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Transform API bid response to match frontend expectations
 */
const transformApiBidResponse = (apiResponse: any): BidListResponse => {
  const bids = apiResponse.data?.map((bid: any) => ({
    id: bid.id?.toString() || '',
    jobId: bid.job_booking_id?.toString() || '',
    providerId: bid.provider_id?.toString() || '',
    customerId: bid.customer_id?.toString() || '',
    amount: bid.amount || 0,
    description: bid.description || '',
    status: bid.status || 'pending',
    submittedAt: bid.created_at || '',
    updatedAt: bid.updated_at || '',
    createdAt: bid.created_at || '',
    provider: bid.provider ? {
      id: bid.provider.id?.toString() || '',
      firstName: bid.provider.first_name || bid.provider.name || '',
      lastName: bid.provider.last_name || '',
      businessName: bid.provider.business_name || bid.provider.name || '',
      avatar: bid.provider.avatar || '',
      rating: bid.provider.rating || 0,
      specialty: bid.provider.specialty || []
    } : undefined,
    customer: bid.customer ? {
      id: bid.customer.id?.toString() || '',
      firstName: bid.customer.first_name || '',
      lastName: bid.customer.last_name || '',
      avatar: bid.customer.avatar || ''
    } : undefined,
    job: bid.job_booking ? {
      id: bid.job_booking.id?.toString() || '',
      title: bid.job_booking.project_code || 'Job',
      description: bid.job_booking.description || '',
      serviceType: bid.job_booking.service_category || '',
      location: bid.job_booking.address || '',
      scheduledDate: bid.job_booking.schedule_date || ''
    } : undefined
  })) || [];

  const pagination = apiResponse.pagination || {};

  return {
    bids,
    total: pagination.total || bids.length,
    page: pagination.current_page || 1,
    limit: pagination.per_page || 10,
    totalPages: pagination.last_page || 1
  };
};

/**
 * Map frontend sort fields to database column names
 */
const mapSortFieldToDbColumn = (field: string): string => {
  const fieldMap: Record<string, string> = {
    'submittedAt': 'created_at',
    'updatedAt': 'updated_at',
    'createdAt': 'created_at',
    'amount': 'amount',
    'providerRating': 'provider_rating' // if this exists in DB
  };
  return fieldMap[field] || field;
};

/**
 * Get all bids (admin only)
 */
export const getAllBids = async (
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10,
  token?: string
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: mapSortFieldToDbColumn(sort.field),
      sortDirection: sort.direction
    })
  });

  try {
    const response = await apiService<any>(`/api/admin/bids?${params}`, {
      method: 'GET',
      requiresAuth: true,
      headers: {
        'Authorization': token || ''
      }
    });

    if (response.isSuccess && response.data) {
      const transformedData = transformApiBidResponse(response.data);
      return {
        isSuccess: true,
        data: transformedData,
        error: null,
        status: response.status
      };
    }

    return response;
  } catch (error) {
    return {
      isSuccess: false,
      data: null,
      error: 'Failed to fetch bids',
      status: 0
    };
  }
};

/**
 * Get a specific bid by ID
 */
export const getBidById = async (bidId: string, token?: string): Promise<ApiResponse<Bid>> => {
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<Bid>(`/api/bids/${bidId}`, {
    method: 'GET',
    requiresAuth: true,
    headers
  });
};

/**
 * Update a bid - Enhanced for Task 18 Subtasks 18.1, 18.2, 18.3 & 18.4
 * Implements comprehensive authentication, authorization, validation, and business logic
 */
export const updateBid = async (
  bidId: string,
  updateData: UpdateBidRequest,
  token?: string
): Promise<ApiResponse<Bid>> => {
  try {
    // Client-side validation before API call
    if (!bidId || typeof bidId !== 'string') {
      return {
        data: null,
        error: 'Valid bid ID is required',
        status: 400,
        isSuccess: false
      };
    }

    // Validate bid ID format (basic UUID check)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(bidId.trim())) {
      return {
        data: null,
        error: 'Bid ID must be a valid UUID format',
        status: 400,
        isSuccess: false
      };
    }

    // Validate that at least one field is being updated
    if (!updateData || (updateData.amount === undefined && !updateData.description)) {
      return {
        data: null,
        error: 'At least one field (amount or description) must be provided for update',
        status: 400,
        isSuccess: false
      };
    }

    // Validate amount if provided
    if (updateData.amount !== undefined) {
      if (typeof updateData.amount !== 'number' || isNaN(updateData.amount)) {
        return {
          data: null,
          error: 'Bid amount must be a valid number',
          status: 400,
          isSuccess: false
        };
      }

      if (updateData.amount <= 0) {
        return {
          data: null,
          error: 'Bid amount must be greater than 0',
          status: 400,
          isSuccess: false
        };
      }

      if (updateData.amount > 1000000) {
        return {
          data: null,
          error: 'Bid amount cannot exceed $1,000,000',
          status: 400,
          isSuccess: false
        };
      }

      // Check for reasonable decimal places (max 2)
      const decimalPlaces = (updateData.amount.toString().split('.')[1] || '').length;
      if (decimalPlaces > 2) {
        return {
          data: null,
          error: 'Bid amount can have at most 2 decimal places',
          status: 400,
          isSuccess: false
        };
      }
    }

    // Validate description if provided
    if (updateData.description !== undefined) {
      if (typeof updateData.description !== 'string') {
        return {
          data: null,
          error: 'Bid description must be a string',
          status: 400,
          isSuccess: false
        };
      }

      const trimmedDescription = updateData.description.trim();

      if (trimmedDescription.length < 10) {
        return {
          data: null,
          error: 'Bid description must be at least 10 characters long',
          status: 400,
          isSuccess: false
        };
      }

      if (trimmedDescription.length > 2000) {
        return {
          data: null,
          error: 'Bid description cannot exceed 2000 characters',
          status: 400,
          isSuccess: false
        };
      }

      // Check for potentially harmful content (basic XSS protection)
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(trimmedDescription)) {
          return {
            data: null,
            error: 'Bid description contains invalid content',
            status: 400,
            isSuccess: false
          };
        }
      }
    }

    // Prepare the update payload with trimmed description if provided
    const updatePayload: UpdateBidRequest = {};
    if (updateData.amount !== undefined) {
      updatePayload.amount = updateData.amount;
    }
    if (updateData.description !== undefined) {
      updatePayload.description = updateData.description.trim();
    }

    // Make API call with enhanced error handling
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    const response = await apiService<Bid>(`/api/bids/${bidId}`, {
      method: 'PUT',
      body: updatePayload,
      requiresAuth: true,
      headers
    });

    // Enhanced response handling with specific error cases
    if (response.isSuccess && response.data) {
      return {
        ...response,
        data: {
          ...response.data,
          // Ensure consistent data structure
          updatedAt: response.data.updatedAt || new Date().toISOString()
        }
      };
    }

    // Handle specific error cases for better user experience
    if (response.status === 401) {
      return {
        ...response,
        error: 'Authentication required. Please log in to update your bid.'
      };
    }

    if (response.status === 403) {
      return {
        ...response,
        error: 'You do not have permission to update this bid. You can only update your own bids.'
      };
    }

    if (response.status === 404) {
      return {
        ...response,
        error: 'Bid not found. It may have been deleted or does not exist.'
      };
    }

    if (response.status === 409) {
      return {
        ...response,
        error: 'This bid cannot be updated. Only bids with "pending" status can be modified.'
      };
    }

    if (response.status === 422) {
      return {
        ...response,
        error: response.error || 'Invalid bid data provided. Please check your input and try again.'
      };
    }

    return response;

  } catch (error) {
    // Handle network errors and unexpected exceptions
    console.error('Error updating bid:', error);
    return {
      data: null,
      error: 'An unexpected error occurred while updating the bid. Please try again.',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Delete a bid - Enhanced for Task 19 Subtasks 19.1, 19.2, 19.3 & 19.4
 * Implements comprehensive authentication, authorization, validation, and error handling
 * Allows providers to delete their own bids (if status is 'requested') or admins to delete any bid
 */
export const deleteBid = async (
  bidId: string,
  token?: string
): Promise<ApiResponse<BidActionResponse>> => {
  try {
    // Client-side validation before API call
    if (!bidId || typeof bidId !== 'string') {
      return {
        data: null,
        error: 'Valid bid ID is required',
        status: 400,
        isSuccess: false
      };
    }

    // Validate bid ID format (basic UUID check)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(bidId.trim())) {
      return {
        data: null,
        error: 'Bid ID must be a valid UUID format',
        status: 400,
        isSuccess: false
      };
    }

    // Prepare headers with authentication token
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Make API call with enhanced error handling
    const response = await apiService<BidActionResponse>(`/api/bids/${bidId}`, {
      method: 'DELETE',
      requiresAuth: true,
      headers
    });

    // Enhanced response handling
    if (response.isSuccess && response.data) {
      return {
        ...response,
        data: {
          ...response.data,
          // Ensure consistent response structure
          success: response.data.success !== undefined ? response.data.success : true,
          message: response.data.message || 'Bid deleted successfully'
        }
      };
    }

    // Handle specific error cases for better user experience
    if (response.status === 401) {
      return {
        ...response,
        error: 'Authentication required. Please log in to delete your bid.'
      };
    }

    if (response.status === 403) {
      return {
        ...response,
        error: 'You do not have permission to delete this bid. Providers can only delete their own bids in \'requested\' status.'
      };
    }

    if (response.status === 404) {
      return {
        ...response,
        error: 'Bid not found. It may have already been deleted or does not exist.'
      };
    }

    if (response.status === 409) {
      return {
        ...response,
        error: 'This bid cannot be deleted. Only bids in \'requested\' status can be deleted.'
      };
    }

    if (response.status === 422) {
      return {
        ...response,
        error: response.error || 'Invalid request. Please check your permissions and try again.'
      };
    }

    return response;

  } catch (error) {
    // Handle network errors and unexpected exceptions
    console.error('Error deleting bid:', error);
    return {
      data: null,
      error: 'An unexpected error occurred while deleting the bid. Please try again.',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Withdraw a bid
 */
export const withdrawBid = async (bidId: string, token?: string): Promise<ApiResponse<BidActionResponse>> => {
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  return apiService<BidActionResponse>(`/api/bids/${bidId}/withdraw`, {
    method: 'POST',
    requiresAuth: true,
    headers
  });
};

/**
 * Update a provider bid using the provider-specific endpoint
 * PUT /api/provider/bids/{bid_id}
 */
export const updateProviderBid = async (
  bidId: string,
  updateData: {
    amount: number;
    description: string;
    estimated_completion_time: string;
  },
  token?: string
): Promise<ApiResponse<Bid>> => {
  try {
    // Client-side validation
    if (!bidId || typeof bidId !== 'string') {
      return {
        data: null,
        error: 'Valid bid ID is required',
        status: 400,
        isSuccess: false
      };
    }

    if (!updateData.amount || updateData.amount <= 0) {
      return {
        data: null,
        error: 'Valid amount is required',
        status: 400,
        isSuccess: false
      };
    }

    if (!updateData.description || updateData.description.trim().length === 0) {
      return {
        data: null,
        error: 'Description is required',
        status: 400,
        isSuccess: false
      };
    }

    if (!updateData.estimated_completion_time) {
      return {
        data: null,
        error: 'Estimated completion time is required',
        status: 400,
        isSuccess: false
      };
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    const response = await apiService<Bid>(`/api/provider/bids/${bidId}`, {
      method: 'PUT',
      body: updateData,
      requiresAuth: true,
      headers
    });

    return response;
  } catch (error) {
    console.error('Error updating provider bid:', error);
    return {
      data: null,
      error: 'Failed to update bid',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Delete/Withdraw a provider bid using the provider-specific endpoint
 * DELETE /provider/bids/{bid_id}
 */
export const deleteProviderBid = async (
  bidId: string,
  token?: string
): Promise<ApiResponse<BidActionResponse>> => {
  try {
    if (!bidId || typeof bidId !== 'string') {
      return {
        data: null,
        error: 'Valid bid ID is required',
        status: 400,
        isSuccess: false
      };
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    const response = await apiService<BidActionResponse>(`/api/provider/bids/${bidId}`, {
      method: 'DELETE',
      requiresAuth: true,
      headers
    });

    return response;
  } catch (error) {
    console.error('Error deleting provider bid:', error);
    return {
      data: null,
      error: 'Failed to withdraw bid',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Accept a bid (customer only)
 * Uses the dedicated accept-bid endpoint as per API documentation
 * Sends bid_id and optional notes in the request body
 */
export const acceptBid = async (
  jobBookingId: string,
  bidId: string,
  token?: string,
  notes?: string
): Promise<ApiResponse<BidActionResponse>> => {
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  const payload: { bid_id: string; notes?: string } = {
    bid_id: bidId
  };

  if (notes) {
    payload.notes = notes;
  }

  return apiService<BidActionResponse>(`/api/job-bookings/${jobBookingId}/accept-bid`, {
    method: 'POST',
    body: payload,
    requiresAuth: true,
    headers
  });
};

/**
 * Reject a bid (customer only)
 * Uses the dedicated reject-bid endpoint as per API documentation
 * Sends bid_id and optional notes in the request body
 */
export const rejectBid = async (
  jobBookingId: string,
  bidId: string,
  token?: string,
  notes?: string
): Promise<ApiResponse<BidActionResponse>> => {
  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  const payload: { bid_id: string; notes?: string } = {
    bid_id: bidId
  };

  if (notes) {
    payload.notes = notes;
  }

  return apiService<BidActionResponse>(`/api/job-bookings/${jobBookingId}/reject-bid`, {
    method: 'POST',
    body: payload,
    requiresAuth: true,
    headers
  });
};

/**
 * Update Bid Status (Accept/Reject) - Task 22 Implementation
 * Implements the PATCH /api/bids/{bid_id}/status endpoint as per BID_API_DOCUMENTATION.md section 4.7
 * Allows a consumer to accept or reject a bid for their service request
 * Enhanced with comprehensive authentication, authorization, validation, and error handling
 */
export const updateBidStatus = async (
  bidId: string,
  statusData: UpdateBidStatusRequest,
  token?: string
): Promise<ApiResponse<Bid>> => {
  try {
    // Client-side validation before API call
    if (!bidId || typeof bidId !== 'string') {
      return {
        data: null,
        error: 'Valid bid ID is required',
        status: 400,
        isSuccess: false
      };
    }

    // Validate bid ID format (basic UUID check)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(bidId.trim())) {
      return {
        data: null,
        error: 'Bid ID must be a valid UUID format',
        status: 400,
        isSuccess: false
      };
    }

    // Validate status data
    if (!statusData || typeof statusData !== 'object') {
      return {
        data: null,
        error: 'Status data is required',
        status: 400,
        isSuccess: false
      };
    }

    // Validate status value - must be 'accepted' or 'rejected' as per API documentation
    if (!statusData.status || !['accepted', 'rejected'].includes(statusData.status)) {
      return {
        data: null,
        error: 'Status must be either "accepted" or "rejected"',
        status: 400,
        isSuccess: false
      };
    }

    // Prepare headers with authentication token
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    // Make API call with enhanced error handling
    const response = await apiService<Bid>(`/api/bids/${bidId}/status`, {
      method: 'PATCH',
      body: {
        status: statusData.status
      },
      requiresAuth: true,
      headers
    });

    // Handle successful response
    if (response.isSuccess && response.data) {
      return {
        data: response.data,
        error: null,
        status: response.status,
        isSuccess: true
      };
    }

    // Handle API error response
    return {
      data: null,
      error: response.error || 'Failed to update bid status',
      status: response.status,
      isSuccess: false
    };

  } catch (error) {
    console.error('Error updating bid status:', error);

    // Handle different types of errors
    if (error instanceof Error) {
      return {
        data: null,
        error: error.message,
        status: 500,
        isSuccess: false
      };
    }

    return {
      data: null,
      error: 'An unexpected error occurred while updating bid status',
      status: 500,
      isSuccess: false
    };
  }
};

/**
 * Transform API bid statistics response to match frontend expectations
 */
const transformApiBidStatsResponse = (apiResponse: any): BidStatistics => {
  const overview = apiResponse.data?.overview || {};
  const financial = apiResponse.data?.financial || {};

  return {
    totalBids: overview.total_bids || 0,
    pendingBids: overview.pending_bids || 0,
    acceptedBids: overview.accepted_bids || 0,
    rejectedBids: overview.rejected_bids || 0,
    withdrawnBids: 0, // API doesn't provide this, default to 0
    averageBidAmount: financial.average_bid_amount || 0,
    totalBidValue: financial.total_bid_value || 0,
    bidsByStatus: {
      pending: overview.pending_bids || 0,
      accepted: overview.accepted_bids || 0,
      rejected: overview.rejected_bids || 0,
      withdrawn: 0 // API doesn't provide this, default to 0
    },
    bidsByMonth: [] // API doesn't provide monthly data, return empty array
  };
};

/**
 * Get bid statistics (admin only)
 */
export const getBidStatistics = async (
  filters?: BidFilters,
  token?: string
): Promise<ApiResponse<BidStatistics>> => {
  const params = new URLSearchParams(
    filters ? Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    ) : {}
  );

  const headers: Record<string, string> = {};
  if (token) {
    headers['Authorization'] = token;
  }

  try {
    const response = await apiService<any>(`/api/admin/bid-stats?${params}`, {
      method: 'GET',
      requiresAuth: true,
      headers
    });

    if (response.isSuccess && response.data) {
      const transformedData = transformApiBidStatsResponse(response.data);
      return {
        isSuccess: true,
        data: transformedData,
        error: null,
        status: response.status
      };
    }

    return response;
  } catch (error) {
    return {
      isSuccess: false,
      data: null,
      error: 'Failed to fetch bid statistics',
      status: 0
    };
  }
};